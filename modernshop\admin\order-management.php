<?php
/**
 * Enhanced Order Management Functions
 * This file contains all the order management functionality for admin
 */

require_once '../config/db-sqlite.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Require admin access
require_admin();

/**
 * Get order details with all related information
 */
function getOrderDetails($pdo, $order_id) {
    try {
        // Get order information
        $stmt = $pdo->prepare("
            SELECT o.*, u.username, u.full_name, u.email, u.phone,
                   ba.first_name as billing_first_name, ba.last_name as billing_last_name,
                   ba.address_line_1 as billing_address_1, ba.city as billing_city,
                   ba.state as billing_state, ba.postal_code as billing_postal_code,
                   sa.first_name as shipping_first_name, sa.last_name as shipping_last_name,
                   sa.address_line_1 as shipping_address_1, sa.city as shipping_city,
                   sa.state as shipping_state, sa.postal_code as shipping_postal_code
            FROM orders o
            JOIN users u ON o.user_id = u.id
            LEFT JOIN customer_addresses ba ON o.billing_address_id = ba.id
            LEFT JOIN customer_addresses sa ON o.shipping_address_id = sa.id
            WHERE o.id = ?
        ");
        $stmt->execute([$order_id]);
        $order = $stmt->fetch();
        
        if (!$order) {
            return null;
        }
        
        // Get order items
        $stmt = $pdo->prepare("
            SELECT oi.*, p.image, p.sku
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.id
            WHERE oi.order_id = ?
        ");
        $stmt->execute([$order_id]);
        $order['items'] = $stmt->fetchAll();
        
        // Get status history
        $stmt = $pdo->prepare("
            SELECT osh.*, u.full_name as changed_by_name
            FROM order_status_history osh
            LEFT JOIN users u ON osh.changed_by = u.id
            WHERE osh.order_id = ?
            ORDER BY osh.created_at DESC
        ");
        $stmt->execute([$order_id]);
        $order['status_history'] = $stmt->fetchAll();
        
        return $order;
    } catch (Exception $e) {
        return null;
    }
}

/**
 * Update order status
 */
function updateOrderStatus($pdo, $order_id, $new_status, $comment = '', $admin_id = null) {
    try {
        $pdo->beginTransaction();
        
        // Get current status
        $stmt = $pdo->prepare("SELECT status FROM orders WHERE id = ?");
        $stmt->execute([$order_id]);
        $current_order = $stmt->fetch();
        
        if (!$current_order) {
            throw new Exception("Order not found");
        }
        
        $old_status = $current_order['status'];
        
        // Update order status
        $stmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$new_status, $order_id]);
        
        // Add to status history
        $stmt = $pdo->prepare("
            INSERT INTO order_status_history (order_id, old_status, new_status, comment, changed_by)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$order_id, $old_status, $new_status, $comment, $admin_id]);
        
        // Log admin activity
        if ($admin_id) {
            logAdminActivity($pdo, $admin_id, 'update_order_status', 'order', $order_id, 
                ['old_status' => $old_status], ['new_status' => $new_status, 'comment' => $comment]);
        }
        
        $pdo->commit();
        return true;
    } catch (Exception $e) {
        $pdo->rollback();
        return false;
    }
}

/**
 * Create new order manually by admin
 */
function createAdminOrder($pdo, $customer_id, $items, $admin_id, $notes = '') {
    try {
        $pdo->beginTransaction();
        
        // Generate order number
        $order_number = 'ADM-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        // Calculate totals
        $subtotal = 0;
        foreach ($items as $item) {
            $subtotal += $item['quantity'] * $item['unit_price'];
        }
        
        $tax_amount = $subtotal * 0.08; // 8% tax
        $shipping_amount = $subtotal >= 50 ? 0 : 9.99; // Free shipping over $50
        $total_amount = $subtotal + $tax_amount + $shipping_amount;
        
        // Get customer's default address
        $stmt = $pdo->prepare("
            SELECT id FROM customer_addresses 
            WHERE user_id = ? AND is_default = 1 
            LIMIT 1
        ");
        $stmt->execute([$customer_id]);
        $address = $stmt->fetch();
        $address_id = $address ? $address['id'] : null;
        
        // Create order
        $stmt = $pdo->prepare("
            INSERT INTO orders (
                order_number, user_id, status, payment_status, subtotal, tax_amount, 
                shipping_amount, total_amount, notes, admin_notes, billing_address_id, 
                shipping_address_id, created_by_admin, admin_id
            ) VALUES (?, ?, 'pending', 'pending', ?, ?, ?, ?, ?, ?, ?, ?, 1, ?)
        ");
        $stmt->execute([
            $order_number, $customer_id, $subtotal, $tax_amount, $shipping_amount, 
            $total_amount, $notes, 'Created by admin', $address_id, $address_id, $admin_id
        ]);
        
        $order_id = $pdo->lastInsertId();
        
        // Add order items
        foreach ($items as $item) {
            $stmt = $pdo->prepare("
                INSERT INTO order_items (order_id, product_id, product_name, product_sku, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $order_id, $item['product_id'], $item['product_name'], $item['product_sku'],
                $item['quantity'], $item['unit_price'], $item['quantity'] * $item['unit_price']
            ]);
            
            // Update product stock
            $stmt = $pdo->prepare("UPDATE products SET stock = stock - ? WHERE id = ?");
            $stmt->execute([$item['quantity'], $item['product_id']]);
            
            // Log inventory movement
            $stmt = $pdo->prepare("
                INSERT INTO inventory_movements (product_id, type, quantity, reason, reference_type, reference_id, admin_id)
                VALUES (?, 'out', ?, 'Admin order creation', 'order', ?, ?)
            ");
            $stmt->execute([$item['product_id'], $item['quantity'], $order_id, $admin_id]);
        }
        
        // Add initial status history
        $stmt = $pdo->prepare("
            INSERT INTO order_status_history (order_id, old_status, new_status, comment, changed_by)
            VALUES (?, NULL, 'pending', 'Order created by admin', ?)
        ");
        $stmt->execute([$order_id, $admin_id]);
        
        // Log admin activity
        logAdminActivity($pdo, $admin_id, 'create_order', 'order', $order_id, 
            [], ['order_number' => $order_number, 'customer_id' => $customer_id, 'total' => $total_amount]);
        
        $pdo->commit();
        return $order_id;
    } catch (Exception $e) {
        $pdo->rollback();
        throw $e;
    }
}

/**
 * Log admin activity
 */
function logAdminActivity($pdo, $admin_id, $action, $entity_type, $entity_id, $old_values = [], $new_values = []) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO admin_activity_log (admin_id, action, entity_type, entity_id, old_values, new_values, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $admin_id, $action, $entity_type, $entity_id,
            json_encode($old_values), json_encode($new_values),
            $_SERVER['REMOTE_ADDR'] ?? '', $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        // Log error but don't fail the main operation
        error_log("Failed to log admin activity: " . $e->getMessage());
    }
}

/**
 * Get customers for order creation
 */
function getCustomersForOrder($pdo) {
    try {
        $stmt = $pdo->query("
            SELECT id, full_name, email, username
            FROM users 
            WHERE role = 'user' 
            ORDER BY full_name
        ");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Get products for order creation
 */
function getProductsForOrder($pdo, $admin_id = null) {
    try {
        $sql = "
            SELECT id, name, price, stock, sku
            FROM products 
            WHERE status = 'active' AND stock > 0
        ";
        
        if ($admin_id) {
            $sql .= " AND (admin_id = ? OR admin_id IS NULL)";
            $stmt = $pdo->prepare($sql . " ORDER BY name");
            $stmt->execute([$admin_id]);
        } else {
            $stmt = $pdo->query($sql . " ORDER BY name");
        }
        
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Handle AJAX requests
 */
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'];
    $admin_id = $_SESSION['user_id'];
    
    switch ($action) {
        case 'update_status':
            $order_id = (int)$_POST['order_id'];
            $new_status = sanitize_input($_POST['status']);
            $comment = sanitize_input($_POST['comment'] ?? '');
            
            $result = updateOrderStatus($pdo, $order_id, $new_status, $comment, $admin_id);
            echo json_encode(['success' => $result]);
            break;
            
        case 'get_order_details':
            $order_id = (int)$_POST['order_id'];
            $order = getOrderDetails($pdo, $order_id);
            echo json_encode(['success' => $order !== null, 'order' => $order]);
            break;
            
        case 'create_order':
            try {
                $customer_id = (int)$_POST['customer_id'];
                $items = json_decode($_POST['items'], true);
                $notes = sanitize_input($_POST['notes'] ?? '');
                
                $order_id = createAdminOrder($pdo, $customer_id, $items, $admin_id, $notes);
                echo json_encode(['success' => true, 'order_id' => $order_id]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
    exit;
}
?>
