<?php
$page_title = "Manage Orders";
$page_description = "View and manage customer orders";
$additional_css = ['../css/admin.css', '../css/animations.css'];

require_once '../config/db-sqlite.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Require admin access
require_admin();

$action = $_GET['action'] ?? 'list';
$admin_id = $_SESSION['user_id'];

// Get orders based on action
$orders = [];

if ($action === 'list') {
    // Get all orders with enhanced information
    try {
        $stmt = $pdo->query("
            SELECT o.*, u.username, u.full_name, u.email
            FROM orders o 
            JOIN users u ON o.user_id = u.id 
            ORDER BY o.order_date DESC
        ");
        $orders = $stmt->fetchAll();
    } catch (Exception $e) {
        $orders = [];
        set_flash_message('Error loading orders: ' . $e->getMessage(), 'error');
    }
}

include '../includes/admin-header.php';
?>

<div class="admin-layout">
    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="admin-logo">
            <h2>ModernShop Admin</h2>
        </div>
        
        <nav class="admin-nav">
            <div class="admin-nav-section">
                <div class="admin-nav-title">Dashboard</div>
                <a href="index.php" class="admin-nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    Overview
                </a>
            </div>
            
            <div class="admin-nav-section">
                <div class="admin-nav-title">Catalog</div>
                <a href="products.php" class="admin-nav-item">
                    <i class="fas fa-box"></i>
                    Products
                </a>
                <a href="categories.php" class="admin-nav-item">
                    <i class="fas fa-tags"></i>
                    Categories
                </a>
            </div>
            
            <div class="admin-nav-section">
                <div class="admin-nav-title">Sales</div>
                <a href="orders.php" class="admin-nav-item active">
                    <i class="fas fa-shopping-cart"></i>
                    Orders
                </a>
                <a href="customers.php" class="admin-nav-item">
                    <i class="fas fa-users"></i>
                    Customers
                </a>
            </div>
            
            <div class="admin-nav-section">
                <div class="admin-nav-title">Account</div>
                <a href="../logout.php" class="admin-nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </nav>
    </aside>
    
    <!-- Main Content -->
    <main class="admin-main">
        <!-- Flash Messages -->
        <?php display_flash_message(); ?>
        
        <!-- Page Header -->
        <div class="admin-page-header animate-fade-in-down">
            <h1 class="admin-page-title">Orders</h1>
            <div class="admin-page-actions">
                <button class="btn btn-outline" onclick="exportOrders()">
                    <i class="fas fa-download"></i>
                    Export
                </button>
                <button class="btn btn-primary" onclick="showNotification('Order creation feature available after full setup', 'info')">
                    <i class="fas fa-plus"></i>
                    Create Order
                </button>
            </div>
        </div>

        <!-- Orders Statistics -->
        <div class="dashboard-grid animate-fade-in-up" style="margin-bottom: var(--space-8);">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="dashboard-card-title">Total Orders</div>
                    <div class="dashboard-card-icon primary">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="dashboard-card-value"><?php echo count($orders); ?></div>
                <div class="dashboard-card-change positive">
                    <i class="fas fa-arrow-up"></i>
                    Active orders
                </div>
            </div>
            
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="dashboard-card-title">Total Revenue</div>
                    <div class="dashboard-card-icon success">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
                <div class="dashboard-card-value">
                    <?php 
                    $total_revenue = array_sum(array_column($orders, 'total_amount'));
                    echo format_price($total_revenue); 
                    ?>
                </div>
                <div class="dashboard-card-change positive">
                    <i class="fas fa-arrow-up"></i>
                    All time
                </div>
            </div>
            
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="dashboard-card-title">Average Order</div>
                    <div class="dashboard-card-icon warning">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="dashboard-card-value">
                    <?php 
                    $avg_order = count($orders) > 0 ? $total_revenue / count($orders) : 0;
                    echo format_price($avg_order); 
                    ?>
                </div>
                <div class="dashboard-card-change positive">
                    <i class="fas fa-arrow-up"></i>
                    Average value
                </div>
            </div>
            
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="dashboard-card-title">Pending Orders</div>
                    <div class="dashboard-card-icon error">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="dashboard-card-value">
                    <?php 
                    $pending_orders = array_filter($orders, function($order) {
                        return isset($order['status']) && $order['status'] === 'pending';
                    });
                    echo count($pending_orders);
                    ?>
                </div>
                <div class="dashboard-card-change neutral">
                    <i class="fas fa-clock"></i>
                    Need attention
                </div>
            </div>
        </div>
        
        <!-- Orders List -->
        <?php if (empty($orders)): ?>
            <div class="data-table-container animate-fade-in-up">
                <div class="admin-empty">
                    <i class="fas fa-shopping-cart"></i>
                    <h3>No orders yet</h3>
                    <p>Orders will appear here when customers make purchases.</p>
                    <p><strong>Sample order data should be available if database setup was completed.</strong></p>
                    <div style="margin-top: 20px;">
                        <a href="index.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="data-table-container animate-fade-in-up">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th>Status</th>
                            <th>Total</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($order['order_number'] ?? 'ORD-' . str_pad($order['id'], 6, '0', STR_PAD_LEFT)); ?></strong>
                                    <?php if (isset($order['created_by_admin']) && $order['created_by_admin']): ?>
                                        <span class="badge badge-info">Admin Created</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($order['full_name']); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($order['email']); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $order['status'] ?? 'pending'; ?>">
                                        <?php echo ucfirst($order['status'] ?? 'pending'); ?>
                                    </span>
                                </td>
                                <td><strong><?php echo format_price($order['total_amount'] ?? 0); ?></strong></td>
                                <td><?php echo date('M j, Y', strtotime($order['order_date'])); ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline" onclick="viewOrderDetails(<?php echo $order['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-primary" onclick="updateOrderStatus(<?php echo $order['id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </main>
</div>

<!-- JavaScript -->
<script src="../js/main.js"></script>
<script>
function exportOrders() {
    const orders = <?php echo json_encode($orders); ?>;
    if (orders.length === 0) {
        showNotification('No orders to export', 'warning');
        return;
    }
    
    let csv = 'Order Number,Customer,Email,Status,Total,Date\n';
    orders.forEach(order => {
        const orderNumber = order.order_number || `ORD-${String(order.id).padStart(6, '0')}`;
        csv += `"${orderNumber}","${order.full_name}","${order.email}","${order.status || 'pending'}","${order.total_amount || 0}","${order.order_date}"\n`;
    });
    
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `orders-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    showNotification('Orders exported successfully!', 'success');
}

function viewOrderDetails(orderId) {
    showNotification(`Order details for Order #${orderId} - Feature coming soon!`, 'info');
}

function updateOrderStatus(orderId) {
    showNotification(`Order status update for Order #${orderId} - Feature coming soon!`, 'info');
}

document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.dashboard-card, .data-table-container');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.animation = `fadeInUp 0.6s ease-out forwards`;
        card.style.animationDelay = `${index * 0.1}s`;
    });
});
</script>

</body>
</html>