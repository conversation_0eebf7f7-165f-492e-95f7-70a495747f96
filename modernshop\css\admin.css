/* Admin Panel Styling */

/* Admin Layout */
.admin-layout {
    /* Remove grid to fix layout issues */
    /* grid-template-columns: 280px 1fr; */
    min-height: 100vh;
    background: var(--color-gray-50);
}

.admin-sidebar {
    background: var(--color-white);
    border-right: 1px solid var(--color-gray-200);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    overflow-y: auto;
    z-index: var(--z-fixed);
}

.admin-main {
    margin-left: 280px;
    padding: var(--space-6);
    min-height: 100vh;
}

/* Admin Header */
.admin-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-gray-200);
    background: var(--color-white);
}

.admin-logo {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.admin-user-info {
    margin-top: var(--space-4);
    padding: var(--space-3);
    background: var(--color-gray-50);
    border-radius: var(--radius-md);
}

.admin-user-name {
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
    font-size: var(--font-size-sm);
}

.admin-user-role {
    color: var(--color-gray-600);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Admin Navigation */
.admin-nav {
    padding: var(--space-4) 0;
}

.admin-nav-section {
    margin-bottom: var(--space-6);
}

.admin-nav-title {
    padding: 0 var(--space-6) var(--space-2);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-500);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.admin-nav-item {
    display: block;
    padding: var(--space-3) var(--space-6);
    color: var(--color-gray-700);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
    border-left: 3px solid transparent;
}

.admin-nav-item:hover {
    background: var(--color-gray-50);
    color: var(--color-primary);
    border-left-color: var(--color-primary);
}

.admin-nav-item.active {
    background: var(--color-primary-50);
    color: var(--color-primary);
    border-left-color: var(--color-primary);
}

.admin-nav-item i {
    width: 20px;
    margin-right: var(--space-3);
}

/* Dashboard Cards */
.dashboard-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.dashboard-card {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-gray-200);
    transition: all var(--transition-normal);
    flex: 1 1 280px;
    min-width: 280px;
}

.dashboard-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.dashboard-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
}

.dashboard-card-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.dashboard-card-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--color-white);
}

.dashboard-card-icon.primary { background: var(--color-primary); }
.dashboard-card-icon.success { background: var(--color-success); }
.dashboard-card-icon.warning { background: var(--color-warning); }
.dashboard-card-icon.error { background: var(--color-error); }

.dashboard-card-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-900);
    margin-bottom: var(--space-2);
}

.dashboard-card-change {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.dashboard-card-change.positive {
    color: var(--color-success);
}

.dashboard-card-change.negative {
    color: var(--color-error);
}

/* Data Tables */
.data-table-container {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-gray-200);
    overflow: hidden;
}

.data-table-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-gray-200);
    background: var(--color-gray-50);
}

.data-table-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
    margin: 0;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: var(--color-gray-50);
    padding: var(--space-4) var(--space-6);
    text-align: left;
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-700);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid var(--color-gray-200);
}

.data-table td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--color-gray-200);
    color: var(--color-gray-900);
}

.data-table tr:hover {
    background: var(--color-gray-50);
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* Form Styling for Admin */
.admin-form {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-gray-200);
}

.admin-form-header {
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid var(--color-gray-200);
}

.admin-form-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-900);
    margin: 0;
}

.admin-form-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-6);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

.btn-icon {
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.active {
    background: var(--color-success-100);
    color: var(--color-success-700);
}

.status-badge.inactive {
    background: var(--color-gray-100);
    color: var(--color-gray-700);
}

.status-badge.pending {
    background: var(--color-warning-100);
    color: var(--color-warning-700);
}

/* Page Header */
.admin-page-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid var(--color-gray-200);
}

.admin-page-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-900);
    margin: 0;
}

.admin-page-actions {
    display: flex;
    gap: var(--space-3);
}

/* Search and Filters */
.admin-filters {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
    padding: var(--space-4);
    background: var(--color-white);
    border-radius: var(--radius-md);
    border: 1px solid var(--color-gray-200);
}

.admin-search {
    flex: 1;
    max-width: 400px;
}

.admin-search input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--color-gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 1024px) {
    /* .admin-layout - no responsive changes needed */

    .admin-sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
    }

    .admin-sidebar.open {
        transform: translateX(0);
    }

    .admin-main {
        margin-left: 0;
        padding: var(--space-4);
    }

    .dashboard-grid {
        flex-direction: column;
    }

    .admin-form-grid {
        flex-direction: column;
    }
    
    .admin-page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-4);
    }
    
    .admin-filters {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .admin-main {
        padding: var(--space-3);
    }
    
    .dashboard-card {
        padding: var(--space-4);
    }
    
    .admin-form {
        padding: var(--space-6);
    }
    
    .data-table-container {
        overflow-x: auto;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: var(--space-1);
    }
}

/* Loading States */
.admin-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-12);
    color: var(--color-gray-500);
}

.admin-loading .loading-spinner {
    margin-right: var(--space-3);
}

/* Empty States */
.admin-empty {
    text-align: center;
    padding: var(--space-12);
    color: var(--color-gray-500);
}

.admin-empty i {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--space-4);
    color: var(--color-gray-400);
}

/* Modal Styles */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    color: var(--color-gray-400);
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--color-gray-100);
    color: var(--color-gray-600);
}

.modal-body {
    padding: var(--space-6);
}

.modal-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--color-gray-200);
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
}

/* Order Management Styles */
.order-details {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.order-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
}

.order-info-section h4 {
    margin-bottom: var(--space-3);
    color: var(--color-gray-900);
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.order-info-section h4 i {
    color: var(--color-primary);
}

.info-item {
    background: var(--color-gray-50);
    padding: var(--space-4);
    border-radius: var(--radius-md);
    border: 1px solid var(--color-gray-200);
}

.status-update-form {
    display: flex;
    gap: var(--space-3);
    align-items: center;
}

.status-update-form select {
    flex: 1;
}

/* Order Items Table */
.order-items-table {
    overflow-x: auto;
}

.product-info {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.product-thumb {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: var(--radius-sm);
    border: 1px solid var(--color-gray-200);
}

.total-row {
    background: var(--color-gray-50);
    font-weight: var(--font-weight-bold);
}

/* Status Timeline */
.status-timeline {
    position: relative;
    padding-left: var(--space-8);
}

.timeline-item {
    position: relative;
    padding-bottom: var(--space-6);
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -var(--space-8);
    top: var(--space-6);
    width: 2px;
    height: calc(100% - var(--space-6));
    background: var(--color-gray-300);
}

.timeline-marker {
    position: absolute;
    left: -var(--space-8);
    top: 0;
    width: 12px;
    height: 12px;
    background: var(--color-primary);
    border-radius: 50%;
    border: 3px solid var(--color-white);
    box-shadow: 0 0 0 2px var(--color-primary);
    transform: translateX(-50%);
}

.timeline-content {
    background: var(--color-white);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--radius-md);
    padding: var(--space-4);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-2);
}

.timeline-date {
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
}

.timeline-comment {
    color: var(--color-gray-700);
    margin-bottom: var(--space-2);
}

.timeline-user {
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
    font-style: italic;
}

/* Order Creation Form */
.form-section {
    margin-bottom: var(--space-8);
}

.form-section h4 {
    margin-bottom: var(--space-4);
    color: var(--color-gray-900);
    font-size: var(--font-size-lg);
    border-bottom: 2px solid var(--color-primary);
    padding-bottom: var(--space-2);
}

.order-item-row {
    background: var(--color-gray-50);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--radius-md);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
}

.order-item-row:hover {
    border-color: var(--color-primary);
    background: var(--color-primary-50);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-transform: capitalize;
}

.status-badge.pending {
    background: var(--color-yellow-100);
    color: var(--color-yellow-800);
}

.status-badge.processing {
    background: var(--color-blue-100);
    color: var(--color-blue-800);
}

.status-badge.shipped {
    background: var(--color-purple-100);
    color: var(--color-purple-800);
}

.status-badge.delivered {
    background: var(--color-green-100);
    color: var(--color-green-800);
}

.status-badge.cancelled {
    background: var(--color-red-100);
    color: var(--color-red-800);
}

.status-badge.refunded {
    background: var(--color-gray-100);
    color: var(--color-gray-800);
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-info {
    background: var(--color-blue-100);
    color: var(--color-blue-800);
}

.badge-secondary {
    background: var(--color-gray-100);
    color: var(--color-gray-800);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--space-2);
}

/* Text Utilities */
.text-muted {
    color: var(--color-gray-600);
}

.text-right {
    text-align: right;
}

/* Responsive Design */
@media (max-width: 768px) {
    .order-info-grid {
        grid-template-columns: 1fr;
    }

    .status-update-form {
        flex-direction: column;
        align-items: stretch;
    }

    .timeline-item {
        padding-left: 0;
    }

    .status-timeline {
        padding-left: var(--space-4);
    }
}

.admin-empty h3 {
    margin-bottom: var(--space-2);
    color: var(--color-gray-600);
}

.admin-empty p {
    margin-bottom: var(--space-6);
}
