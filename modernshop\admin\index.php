<?php
$page_title = "Admin Dashboard";
$page_description = "ModernShop Administration Panel";
$additional_css = ['../css/admin.css', '../css/animations.css'];

require_once '../config/db-sqlite.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Require admin access
require_admin();

// Get dashboard statistics
try {
    $admin_id = $_SESSION['user_id'];

    // Total products (for current admin)
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products WHERE admin_id = ? OR admin_id IS NULL");
    $stmt->execute([$admin_id]);
    $total_products = $stmt->fetch()['count'];

    // Total orders (for current admin's products)
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count FROM orders o
        JOIN products p ON o.product_id = p.id
        WHERE p.admin_id = ? OR p.admin_id IS NULL
    ");
    $stmt->execute([$admin_id]);
    $total_orders = $stmt->fetch()['count'];

    // Total users
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role = 'user'");
    $total_users = $stmt->fetch()['count'];

    // Total revenue (for current admin's products)
    $stmt = $pdo->prepare("
        SELECT SUM(o.total_price) as total FROM orders o
        JOIN products p ON o.product_id = p.id
        WHERE p.admin_id = ? OR p.admin_id IS NULL
    ");
    $stmt->execute([$admin_id]);
    $total_revenue = $stmt->fetch()['total'] ?? 0;
    
    // Recent orders (for current admin's products)
    $stmt = $pdo->prepare("
        SELECT o.*, u.username, p.name as product_name
        FROM orders o
        JOIN users u ON o.user_id = u.id
        JOIN products p ON o.product_id = p.id
        WHERE p.admin_id = ? OR p.admin_id IS NULL
        ORDER BY o.order_date DESC
        LIMIT 5
    ");
    $stmt->execute([$admin_id]);
    $recent_orders = $stmt->fetchAll();

    // Low stock products (for current admin)
    $stmt = $pdo->prepare("SELECT * FROM products WHERE (admin_id = ? OR admin_id IS NULL) AND stock <= 5 ORDER BY stock ASC LIMIT 5");
    $stmt->execute([$admin_id]);
    $low_stock_products = $stmt->fetchAll();
    
} catch (Exception $e) {
    $total_products = $total_orders = $total_users = $total_revenue = 0;
    $recent_orders = $low_stock_products = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - ModernShop</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/main.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="../css/modern-theme.css?v=<?php echo time(); ?>">
    <?php foreach ($additional_css as $css_file): ?>
        <link rel="stylesheet" href="<?php echo $css_file; ?>?v=<?php echo time(); ?>">
    <?php endforeach; ?>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Force override admin grid layout -->
    <style>
        .admin-layout {
            display: block !important;
            /* Remove grid completely */
        }

        .dashboard-grid {
            display: flex !important;
            flex-wrap: wrap !important;
            gap: 1.5rem !important;
            margin-bottom: 2rem !important;
        }

        .dashboard-card {
            flex: 1 1 280px !important;
            min-width: 280px !important;
            max-width: calc(25% - 1.125rem) !important;
        }

        .admin-form-grid {
            display: flex !important;
            flex-wrap: wrap !important;
            gap: 1.5rem !important;
        }

        @media (max-width: 1200px) {
            .dashboard-card {
                max-width: calc(50% - 0.75rem) !important;
            }
        }

        @media (max-width: 768px) {
            .dashboard-card {
                max-width: 100% !important;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <!-- Admin Header -->
            <div class="admin-header">
                <a href="index.php" class="admin-logo">
                    <i class="fas fa-store"></i>
                    ModernShop Admin
                </a>
                
                <div class="admin-user-info">
                    <div class="admin-user-name"><?php echo htmlspecialchars($_SESSION['full_name']); ?></div>
                    <div class="admin-user-role">Administrator</div>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="admin-nav">
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Main</div>
                    <a href="index.php" class="admin-nav-item active">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                    <a href="../index.php" class="admin-nav-item">
                        <i class="fas fa-external-link-alt"></i>
                        View Store
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Catalog</div>
                    <a href="products.php" class="admin-nav-item">
                        <i class="fas fa-box"></i>
                        Products
                    </a>
                    <a href="categories.php" class="admin-nav-item">
                        <i class="fas fa-tags"></i>
                        Categories
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Sales</div>
                    <a href="orders.php" class="admin-nav-item">
                        <i class="fas fa-shopping-cart"></i>
                        Orders
                    </a>
                    <a href="customers.php" class="admin-nav-item">
                        <i class="fas fa-users"></i>
                        Customers
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Account</div>
                    <a href="../logout.php" class="admin-nav-item">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <!-- Page Header -->
            <div class="admin-page-header animate-fade-in-down">
                <h1 class="admin-page-title">Dashboard</h1>
                <div class="admin-page-actions">
                    <button class="btn btn-primary" onclick="openQuickAddModal()">
                        <i class="fas fa-plus"></i>
                        Quick Add Product
                    </button>
                </div>
            </div>
            
            <!-- Dashboard Cards -->
            <div class="dashboard-grid animate-fade-in-up">
                <!-- Total Products -->
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">Total Products</div>
                        <div class="dashboard-card-icon primary">
                            <i class="fas fa-box"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-value"><?php echo number_format($total_products); ?></div>
                    <div class="dashboard-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +12% from last month
                    </div>
                </div>
                
                <!-- Total Orders -->
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">Total Orders</div>
                        <div class="dashboard-card-icon success">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-value"><?php echo number_format($total_orders); ?></div>
                    <div class="dashboard-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +8% from last month
                    </div>
                </div>
                
                <!-- Total Customers -->
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">Total Customers</div>
                        <div class="dashboard-card-icon warning">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-value"><?php echo number_format($total_users); ?></div>
                    <div class="dashboard-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +15% from last month
                    </div>
                </div>
                
                <!-- Total Revenue -->
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">Total Revenue</div>
                        <div class="dashboard-card-icon error">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-value"><?php echo format_price($total_revenue); ?></div>
                    <div class="dashboard-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +23% from last month
                    </div>
                </div>
            </div>
            
            <!-- Content Grid -->
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--space-8); margin-top: var(--space-8);">
                <!-- Recent Orders -->
                <div class="data-table-container animate-fade-in-left">
                    <div class="data-table-header">
                        <h2 class="data-table-title">Recent Orders</h2>
                    </div>
                    
                    <?php if (empty($recent_orders)): ?>
                        <div class="admin-empty">
                            <i class="fas fa-shopping-cart"></i>
                            <h3>No orders yet</h3>
                            <p>Orders will appear here when customers make purchases.</p>
                        </div>
                    <?php else: ?>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Product</th>
                                    <th>Amount</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_orders as $order): ?>
                                    <tr>
                                        <td>
                                            <span style="font-weight: var(--font-weight-semibold);">
                                                #<?php echo str_pad($order['id'], 6, '0', STR_PAD_LEFT); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($order['username']); ?></td>
                                        <td><?php echo htmlspecialchars(truncate_text($order['product_name'], 30)); ?></td>
                                        <td style="font-weight: var(--font-weight-semibold); color: var(--color-primary);">
                                            <?php echo format_price($order['total_price']); ?>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($order['order_date'])); ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-secondary btn-icon" title="View Order">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-primary btn-icon" title="Edit Order">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
                
                <!-- Low Stock Alert -->
                <div class="data-table-container animate-fade-in-right">
                    <div class="data-table-header">
                        <h2 class="data-table-title">Low Stock Alert</h2>
                    </div>
                    
                    <?php if (empty($low_stock_products)): ?>
                        <div class="admin-empty">
                            <i class="fas fa-check-circle"></i>
                            <h3>All Good!</h3>
                            <p>No products are running low on stock.</p>
                        </div>
                    <?php else: ?>
                        <div style="padding: var(--space-4);">
                            <?php foreach ($low_stock_products as $product): ?>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--space-3); border-bottom: 1px solid var(--color-gray-200); last:border-bottom: none;">
                                    <div>
                                        <div style="font-weight: var(--font-weight-semibold); color: var(--color-gray-900);">
                                            <?php echo htmlspecialchars(truncate_text($product['name'], 25)); ?>
                                        </div>
                                        <div style="font-size: var(--font-size-sm); color: var(--color-gray-600);">
                                            <?php echo format_price($product['price']); ?>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="status-badge <?php echo $product['stock'] == 0 ? 'inactive' : 'pending'; ?>">
                                            <?php echo $product['stock']; ?> left
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div style="margin-top: var(--space-8);">
                <h2 style="margin-bottom: var(--space-6); color: var(--color-gray-900);">Quick Actions</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-4);">
                    <a href="products.php?action=add" class="card hover-lift" style="padding: var(--space-6); text-decoration: none; text-align: center;">
                        <div style="font-size: var(--font-size-3xl); color: var(--color-primary); margin-bottom: var(--space-3);">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <h3 style="margin: 0; color: var(--color-gray-900);">Add Product</h3>
                        <p style="margin: var(--space-2) 0 0; color: var(--color-gray-600); font-size: var(--font-size-sm);">
                            Add a new product to your catalog
                        </p>
                    </a>
                    
                    <a href="orders.php" class="card hover-lift" style="padding: var(--space-6); text-decoration: none; text-align: center;">
                        <div style="font-size: var(--font-size-3xl); color: var(--color-success); margin-bottom: var(--space-3);">
                            <i class="fas fa-list-alt"></i>
                        </div>
                        <h3 style="margin: 0; color: var(--color-gray-900);">Manage Orders</h3>
                        <p style="margin: var(--space-2) 0 0; color: var(--color-gray-600); font-size: var(--font-size-sm);">
                            View and manage customer orders
                        </p>
                    </a>
                    
                    <a href="customers.php" class="card hover-lift" style="padding: var(--space-6); text-decoration: none; text-align: center;">
                        <div style="font-size: var(--font-size-3xl); color: var(--color-warning); margin-bottom: var(--space-3);">
                            <i class="fas fa-user-friends"></i>
                        </div>
                        <h3 style="margin: 0; color: var(--color-gray-900);">View Customers</h3>
                        <p style="margin: var(--space-2) 0 0; color: var(--color-gray-600); font-size: var(--font-size-sm);">
                            Manage customer accounts
                        </p>
                    </a>
                    
                    <a href="../index.php" class="card hover-lift" style="padding: var(--space-6); text-decoration: none; text-align: center;">
                        <div style="font-size: var(--font-size-3xl); color: var(--color-error); margin-bottom: var(--space-3);">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                        <h3 style="margin: 0; color: var(--color-gray-900);">View Store</h3>
                        <p style="margin: var(--space-2) 0 0; color: var(--color-gray-600); font-size: var(--font-size-sm);">
                            See your store as customers do
                        </p>
                    </a>
                </div>
            </div>
        </main>
    </div>

    <!-- Quick Add Product Modal -->
    <div id="quickAddModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Quick Add Product</h3>
                <button class="modal-close" onclick="closeQuickAddModal()">&times;</button>
            </div>
            <form id="quickAddForm" method="POST" action="products.php">
                <input type="hidden" name="action" value="add">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                <div class="modal-body">
                    <div class="form-group">
                        <label for="quick_name">Product Name *</label>
                        <input type="text" id="quick_name" name="name" class="form-input" required>
                    </div>

                    <div class="form-group">
                        <label for="quick_price">Price *</label>
                        <input type="number" id="quick_price" name="price" class="form-input" step="0.01" min="0" required>
                    </div>

                    <div class="form-group">
                        <label for="quick_stock">Stock Quantity *</label>
                        <input type="number" id="quick_stock" name="stock" class="form-input" min="0" required>
                    </div>

                    <div class="form-group">
                        <label for="quick_category">Category</label>
                        <select id="quick_category" name="category_id" class="form-input">
                            <option value="">Select Category</option>
                            <?php
                            try {
                                $stmt = $pdo->query("SELECT id, name FROM categories ORDER BY name");
                                $categories = $stmt->fetchAll();
                                foreach ($categories as $category) {
                                    echo "<option value='{$category['id']}'>" . htmlspecialchars($category['name']) . "</option>";
                                }
                            } catch (Exception $e) {
                                echo "<option value=''>Error loading categories</option>";
                            }
                            ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="quick_description">Description</label>
                        <textarea id="quick_description" name="description" class="form-input" rows="3"></textarea>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeQuickAddModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Add Product
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../js/main.js"></script>
    
    <script>
        // Quick Add Modal Functions
        function openQuickAddModal() {
            document.getElementById('quickAddModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeQuickAddModal() {
            document.getElementById('quickAddModal').style.display = 'none';
            document.body.style.overflow = '';
            document.getElementById('quickAddForm').reset();
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('quickAddModal');
            if (e.target === modal) {
                closeQuickAddModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeQuickAddModal();
            }
        });

        // Add stagger animation to dashboard cards
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.animation = `fadeInUp 0.6s ease-out forwards`;
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // Add hover effects to quick action cards
            const actionCards = document.querySelectorAll('.card.hover-lift');
            actionCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
